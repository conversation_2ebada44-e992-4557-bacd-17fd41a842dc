const { query } = require('../database/connection');

class OssuarioService {
  
  // ==================== GESTÃO DE BLOCOS ====================
  
  async createBloco(blocoData) {
    const { codigo_cliente, codigo_estacao, codigo_bloco, nome, descricao, tipo_capacidade } = blocoData;
    
    try {
      // Verificar se o produto é do tipo OSSUARIO
      const produto = await query(
        'SELECT tipo FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2',
        [codigo_cliente, codigo_estacao]
      );
      
      if (produto.rows.length === 0) {
        throw new Error('Produto não encontrado');
      }
      
      if (produto.rows[0].tipo !== 'OSSUARIO') {
        throw new Error('Este produto não é um ossuário');
      }
      
      // Verificar se o código do bloco já existe
      const existingBloco = await query(
        'SELECT id FROM blocos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      if (existingBloco.rows.length > 0) {
        throw new Error('Código do bloco já existe para este ossuário');
      }
      
      // Criar o bloco
      const result = await query(`
        INSERT INTO blocos_ossuarios (codigo_cliente, codigo_estacao, codigo_bloco, nome, descricao, tipo_capacidade)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `, [codigo_cliente, codigo_estacao, codigo_bloco, nome, descricao, tipo_capacidade]);
      
      return result.rows[0];
    } catch (error) {
      console.error('Erro ao criar bloco de ossuário:', error);
      throw error;
    }
  }
  
  async updateBloco(codigo_cliente, codigo_estacao, codigo_bloco, blocoData) {
    const { nome, descricao } = blocoData;
    
    try {
      // Verificar se existem nichos criados (não permite alterar tipo_capacidade)
      const nichosCount = await query(
        'SELECT COUNT(*) FROM nichos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      const result = await query(`
        UPDATE blocos_ossuarios 
        SET nome = $1, descricao = $2, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $3 AND codigo_estacao = $4 AND codigo_bloco = $5
        RETURNING *
      `, [nome, descricao, codigo_cliente, codigo_estacao, codigo_bloco]);
      
      if (result.rows.length === 0) {
        throw new Error('Bloco não encontrado');
      }
      
      return {
        ...result.rows[0],
        has_nichos: parseInt(nichosCount.rows[0].count) > 0
      };
    } catch (error) {
      console.error('Erro ao atualizar bloco de ossuário:', error);
      throw error;
    }
  }
  
  async deleteBloco(codigo_cliente, codigo_estacao, codigo_bloco) {
    try {
      // Verificar se existem sepultamentos ativos
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) FROM sepultamentos_ossuarios 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND ativo = true AND data_remocao IS NULL
      `, [codigo_cliente, codigo_estacao, codigo_bloco]);
      
      if (parseInt(sepultamentosAtivos.rows[0].count) > 0) {
        throw new Error('Não é possível deletar bloco com sepultamentos ativos');
      }
      
      // Deletar em cascata: ranges -> nichos -> urnas -> sepultamentos -> bloco
      await query('BEGIN');
      
      // Deletar sepultamentos
      await query(
        'DELETE FROM sepultamentos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      // Deletar urnas
      await query(
        'DELETE FROM urnas_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      // Deletar nichos
      await query(
        'DELETE FROM nichos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      // Deletar ranges
      await query(
        'DELETE FROM ranges_nichos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      // Deletar bloco
      const result = await query(
        'DELETE FROM blocos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 RETURNING *',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      await query('COMMIT');
      
      return result.rows[0];
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao deletar bloco de ossuário:', error);
      throw error;
    }
  }
  
  async listBlocos(codigo_cliente, codigo_estacao) {
    try {
      const result = await query(`
        SELECT 
          b.*,
          COUNT(n.id) as total_nichos,
          COUNT(u.id) as total_urnas,
          COUNT(CASE WHEN u.disponivel = true THEN 1 END) as urnas_disponiveis,
          COUNT(s.id) as total_sepultamentos
        FROM blocos_ossuarios b
        LEFT JOIN nichos_ossuarios n ON b.codigo_cliente = n.codigo_cliente 
                                     AND b.codigo_estacao = n.codigo_estacao 
                                     AND b.codigo_bloco = n.codigo_bloco
        LEFT JOIN urnas_ossuarios u ON n.codigo_cliente = u.codigo_cliente 
                                    AND n.codigo_estacao = u.codigo_estacao 
                                    AND n.codigo_bloco = u.codigo_bloco 
                                    AND n.numero_nicho = u.numero_nicho
        LEFT JOIN sepultamentos_ossuarios s ON u.codigo_cliente = s.codigo_cliente 
                                            AND u.codigo_estacao = s.codigo_estacao 
                                            AND u.codigo_bloco = s.codigo_bloco 
                                            AND u.numero_nicho = s.numero_nicho 
                                            AND u.letra_urna = s.letra_urna 
                                            AND s.ativo = true 
                                            AND s.data_remocao IS NULL
        WHERE b.codigo_cliente = $1 AND b.codigo_estacao = $2 AND b.ativo = true
        GROUP BY b.id, b.codigo_cliente, b.codigo_estacao, b.codigo_bloco, b.nome, b.descricao, b.tipo_capacidade, b.ativo, b.created_at, b.updated_at
        ORDER BY b.nome
      `, [codigo_cliente, codigo_estacao]);
      
      return result.rows.map(row => ({
        ...row,
        total_nichos: parseInt(row.total_nichos),
        total_urnas: parseInt(row.total_urnas),
        urnas_disponiveis: parseInt(row.urnas_disponiveis),
        total_sepultamentos: parseInt(row.total_sepultamentos),
        tipo_capacidade_label: this.getTipoCapacidadeLabel(row.tipo_capacidade)
      }));
    } catch (error) {
      console.error('Erro ao listar blocos de ossuário:', error);
      throw error;
    }
  }
  
  // ==================== GESTÃO DE RANGES ====================
  
  async createRange(rangeData) {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim } = rangeData;
    
    try {
      await query('BEGIN');
      
      // Buscar tipo_capacidade do bloco
      const bloco = await query(
        'SELECT tipo_capacidade FROM blocos_ossuarios WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3',
        [codigo_cliente, codigo_estacao, codigo_bloco]
      );
      
      if (bloco.rows.length === 0) {
        throw new Error('Bloco não encontrado');
      }
      
      const tipo_capacidade = bloco.rows[0].tipo_capacidade;
      
      // Verificar sobreposição de ranges
      const overlapping = await query(`
        SELECT * FROM ranges_nichos_ossuarios 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND (
          (numero_inicio <= $4 AND numero_fim >= $4) OR
          (numero_inicio <= $5 AND numero_fim >= $5) OR
          (numero_inicio >= $4 AND numero_fim <= $5)
        )
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);
      
      if (overlapping.rows.length > 0) {
        throw new Error('Range sobrepõe com range existente');
      }
      
      // Criar range
      const range = await query(`
        INSERT INTO ranges_nichos_ossuarios (codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);
      
      // Criar nichos e urnas
      const letras_urnas = ['A', 'B', 'C', 'D'];
      let total_nichos = 0;
      let total_urnas = 0;
      
      for (let numero_nicho = numero_inicio; numero_nicho <= numero_fim; numero_nicho++) {
        // Criar nicho
        await query(`
          INSERT INTO nichos_ossuarios (codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho)
          VALUES ($1, $2, $3, $4)
        `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho]);
        
        total_nichos++;
        
        // Criar urnas para este nicho
        for (let i = 0; i < tipo_capacidade; i++) {
          await query(`
            INSERT INTO urnas_ossuarios (codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna, disponivel)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letras_urnas[i], true]);
          
          total_urnas++;
        }
      }
      
      await query('COMMIT');
      
      return {
        range: range.rows[0],
        total_nichos,
        total_urnas,
        tipo_capacidade
      };
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao criar range de nichos:', error);
      throw error;
    }
  }
  
  async deleteRange(rangeId) {
    try {
      await query('BEGIN');

      // Buscar dados do range
      const range = await query('SELECT * FROM ranges_nichos_ossuarios WHERE id = $1', [rangeId]);

      if (range.rows.length === 0) {
        throw new Error('Range não encontrado');
      }

      const { codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim } = range.rows[0];

      // Verificar se existem sepultamentos ativos no range
      const sepultamentosAtivos = await query(`
        SELECT COUNT(*) FROM sepultamentos_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho BETWEEN $4 AND $5
        AND ativo = true AND data_remocao IS NULL
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);

      if (parseInt(sepultamentosAtivos.rows[0].count) > 0) {
        throw new Error('Não é possível deletar range com sepultamentos ativos');
      }

      // Deletar sepultamentos do range
      await query(`
        DELETE FROM sepultamentos_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho BETWEEN $4 AND $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);

      // Deletar urnas do range
      await query(`
        DELETE FROM urnas_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho BETWEEN $4 AND $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);

      // Deletar nichos do range
      await query(`
        DELETE FROM nichos_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho BETWEEN $4 AND $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim]);

      // Deletar range
      const result = await query('DELETE FROM ranges_nichos_ossuarios WHERE id = $1 RETURNING *', [rangeId]);

      await query('COMMIT');

      return result.rows[0];
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao deletar range de nichos:', error);
      throw error;
    }
  }

  async listRanges(codigo_cliente, codigo_estacao, codigo_bloco) {
    try {
      const result = await query(`
        SELECT
          r.*,
          b.tipo_capacidade,
          (r.numero_fim - r.numero_inicio + 1) as total_nichos,
          (r.numero_fim - r.numero_inicio + 1) * b.tipo_capacidade as total_urnas
        FROM ranges_nichos_ossuarios r
        JOIN blocos_ossuarios b ON r.codigo_cliente = b.codigo_cliente
                                AND r.codigo_estacao = b.codigo_estacao
                                AND r.codigo_bloco = b.codigo_bloco
        WHERE r.codigo_cliente = $1 AND r.codigo_estacao = $2 AND r.codigo_bloco = $3 AND r.ativo = true
        ORDER BY r.numero_inicio
      `, [codigo_cliente, codigo_estacao, codigo_bloco]);

      return result.rows.map(row => ({
        ...row,
        total_nichos: parseInt(row.total_nichos),
        total_urnas: parseInt(row.total_urnas)
      }));
    } catch (error) {
      console.error('Erro ao listar ranges de nichos:', error);
      throw error;
    }
  }

  // ==================== GESTÃO DE NICHOS E URNAS ====================

  async listNichos(codigo_cliente, codigo_estacao, codigo_bloco) {
    try {
      const result = await query(`
        SELECT
          n.*,
          COUNT(u.id) as total_urnas,
          COUNT(CASE WHEN u.disponivel = true THEN 1 END) as urnas_disponiveis,
          COUNT(s.id) as sepultamentos_ativos
        FROM nichos_ossuarios n
        LEFT JOIN urnas_ossuarios u ON n.codigo_cliente = u.codigo_cliente
                                    AND n.codigo_estacao = u.codigo_estacao
                                    AND n.codigo_bloco = u.codigo_bloco
                                    AND n.numero_nicho = u.numero_nicho
        LEFT JOIN sepultamentos_ossuarios s ON u.codigo_cliente = s.codigo_cliente
                                            AND u.codigo_estacao = s.codigo_estacao
                                            AND u.codigo_bloco = s.codigo_bloco
                                            AND u.numero_nicho = s.numero_nicho
                                            AND u.letra_urna = s.letra_urna
                                            AND s.ativo = true
                                            AND s.data_remocao IS NULL
        WHERE n.codigo_cliente = $1 AND n.codigo_estacao = $2 AND n.codigo_bloco = $3 AND n.ativo = true
        GROUP BY n.id, n.codigo_cliente, n.codigo_estacao, n.codigo_bloco, n.numero_nicho, n.ativo, n.created_at, n.updated_at
        ORDER BY n.numero_nicho
      `, [codigo_cliente, codigo_estacao, codigo_bloco]);

      return result.rows.map(row => ({
        ...row,
        total_urnas: parseInt(row.total_urnas),
        urnas_disponiveis: parseInt(row.urnas_disponiveis),
        sepultamentos_ativos: parseInt(row.sepultamentos_ativos)
      }));
    } catch (error) {
      console.error('Erro ao listar nichos:', error);
      throw error;
    }
  }

  async listUrnas(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho) {
    try {
      const result = await query(`
        SELECT
          u.*,
          s.nome_sepultado,
          s.data_sepultamento,
          s.data_remocao,
          s.observacoes as observacao_sepultamento,
          CASE
            WHEN s.id IS NOT NULL AND s.data_remocao IS NULL THEN 'ocupada'
            WHEN s.id IS NOT NULL AND s.data_remocao IS NOT NULL THEN 'removida'
            ELSE 'disponivel'
          END as status_urna
        FROM urnas_ossuarios u
        LEFT JOIN sepultamentos_ossuarios s ON u.codigo_cliente = s.codigo_cliente
                                            AND u.codigo_estacao = s.codigo_estacao
                                            AND u.codigo_bloco = s.codigo_bloco
                                            AND u.numero_nicho = s.numero_nicho
                                            AND u.letra_urna = s.letra_urna
                                            AND s.ativo = true
        WHERE u.codigo_cliente = $1 AND u.codigo_estacao = $2 AND u.codigo_bloco = $3 AND u.numero_nicho = $4 AND u.ativo = true
        ORDER BY u.letra_urna
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho]);

      return result.rows;
    } catch (error) {
      console.error('Erro ao listar urnas:', error);
      throw error;
    }
  }

  async listUrnasDisponiveis(codigo_cliente, codigo_estacao) {
    try {
      const result = await query(`
        SELECT
          u.*,
          b.nome as nome_bloco,
          b.tipo_capacidade
        FROM urnas_ossuarios u
        JOIN blocos_ossuarios b ON u.codigo_cliente = b.codigo_cliente
                                AND u.codigo_estacao = b.codigo_estacao
                                AND u.codigo_bloco = b.codigo_bloco
        WHERE u.codigo_cliente = $1 AND u.codigo_estacao = $2
        AND u.disponivel = true AND u.ativo = true
        ORDER BY b.nome, u.numero_nicho, u.letra_urna
      `, [codigo_cliente, codigo_estacao]);

      return result.rows;
    } catch (error) {
      console.error('Erro ao listar urnas disponíveis:', error);
      throw error;
    }
  }

  // ==================== GESTÃO DE SEPULTAMENTOS ====================

  async createSepultamento(sepultamentoData) {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna, nome_sepultado, data_sepultamento, hora_sepultamento, observacoes } = sepultamentoData;

    try {
      await query('BEGIN');

      // Verificar se a urna está disponível
      const urna = await query(`
        SELECT * FROM urnas_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho = $4 AND letra_urna = $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna]);

      if (urna.rows.length === 0) {
        throw new Error('Urna não encontrada');
      }

      if (!urna.rows[0].disponivel) {
        throw new Error('Urna não está disponível');
      }

      // Verificar se já existe sepultamento ativo nesta urna
      const sepultamentoExistente = await query(`
        SELECT id FROM sepultamentos_ossuarios
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho = $4 AND letra_urna = $5
        AND ativo = true AND data_remocao IS NULL
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna]);

      if (sepultamentoExistente.rows.length > 0) {
        throw new Error('Já existe um sepultamento ativo nesta urna');
      }

      // Criar sepultamento
      const result = await query(`
        INSERT INTO sepultamentos_ossuarios (
          codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna,
          nome_sepultado, data_sepultamento, hora_sepultamento, observacoes
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna, nome_sepultado, data_sepultamento, hora_sepultamento, observacoes]);

      // Marcar urna como indisponível
      await query(`
        UPDATE urnas_ossuarios
        SET disponivel = false, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho = $4 AND letra_urna = $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna]);

      await query('COMMIT');

      return result.rows[0];
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao criar sepultamento em ossuário:', error);
      throw error;
    }
  }

  async updateSepultamento(id, sepultamentoData) {
    const { nome_sepultado, data_sepultamento, hora_sepultamento, observacoes } = sepultamentoData;

    try {
      const result = await query(`
        UPDATE sepultamentos_ossuarios
        SET nome_sepultado = $1, data_sepultamento = $2, hora_sepultamento = $3,
            observacoes = $4, updated_at = CURRENT_TIMESTAMP
        WHERE id = $5 AND ativo = true
        RETURNING *
      `, [nome_sepultado, data_sepultamento, hora_sepultamento, observacoes, id]);

      if (result.rows.length === 0) {
        throw new Error('Sepultamento não encontrado');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Erro ao atualizar sepultamento em ossuário:', error);
      throw error;
    }
  }

  async removerSepultamento(id, dataRemocao) {
    try {
      await query('BEGIN');

      // Buscar dados do sepultamento
      const sepultamento = await query('SELECT * FROM sepultamentos_ossuarios WHERE id = $1 AND ativo = true', [id]);

      if (sepultamento.rows.length === 0) {
        throw new Error('Sepultamento não encontrado');
      }

      const { codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna } = sepultamento.rows[0];

      // Marcar data de remoção (mantém histórico)
      const result = await query(`
        UPDATE sepultamentos_ossuarios
        SET data_remocao = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [dataRemocao, id]);

      // Liberar urna
      await query(`
        UPDATE urnas_ossuarios
        SET disponivel = true, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho = $4 AND letra_urna = $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna]);

      await query('COMMIT');

      return result.rows[0];
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao remover sepultamento em ossuário:', error);
      throw error;
    }
  }

  async deleteSepultamento(id) {
    try {
      await query('BEGIN');

      // Buscar dados do sepultamento
      const sepultamento = await query('SELECT * FROM sepultamentos_ossuarios WHERE id = $1', [id]);

      if (sepultamento.rows.length === 0) {
        throw new Error('Sepultamento não encontrado');
      }

      const { codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna } = sepultamento.rows[0];

      // Deletar permanentemente
      const result = await query('DELETE FROM sepultamentos_ossuarios WHERE id = $1 RETURNING *', [id]);

      // Liberar urna
      await query(`
        UPDATE urnas_ossuarios
        SET disponivel = true, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND numero_nicho = $4 AND letra_urna = $5
      `, [codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna]);

      await query('COMMIT');

      return result.rows[0];
    } catch (error) {
      await query('ROLLBACK');
      console.error('Erro ao deletar sepultamento em ossuário:', error);
      throw error;
    }
  }

  async listSepultamentos(filtros) {
    const { codigo_cliente, codigo_estacao, codigo_bloco, incluir_removidos = false, limit = 50, offset = 0 } = filtros;

    try {
      let whereClause = 'WHERE s.codigo_cliente = $1';
      let params = [codigo_cliente];
      let paramIndex = 2;

      if (codigo_estacao) {
        whereClause += ` AND s.codigo_estacao = $${paramIndex}`;
        params.push(codigo_estacao);
        paramIndex++;
      }

      if (codigo_bloco) {
        whereClause += ` AND s.codigo_bloco = $${paramIndex}`;
        params.push(codigo_bloco);
        paramIndex++;
      }

      if (!incluir_removidos) {
        whereClause += ' AND s.data_remocao IS NULL';
      }

      whereClause += ' AND s.ativo = true';

      const result = await query(`
        SELECT
          s.*,
          b.nome as nome_bloco,
          b.tipo_capacidade,
          CASE
            WHEN s.data_remocao IS NOT NULL THEN 'removido'
            ELSE 'ativo'
          END as status_sepultamento
        FROM sepultamentos_ossuarios s
        JOIN blocos_ossuarios b ON s.codigo_cliente = b.codigo_cliente
                                AND s.codigo_estacao = b.codigo_estacao
                                AND s.codigo_bloco = b.codigo_bloco
        ${whereClause}
        ORDER BY s.data_sepultamento DESC, s.numero_nicho, s.letra_urna
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, limit, offset]);

      return result.rows;
    } catch (error) {
      console.error('Erro ao listar sepultamentos em ossuários:', error);
      throw error;
    }
  }

  // ==================== UTILITÁRIOS ====================

  getTipoCapacidadeLabel(tipo_capacidade) {
    const labels = {
      1: 'Simples (1 urna)',
      2: 'Duplo (2 urnas)',
      3: 'Triplo (3 urnas)',
      4: 'Quádruplo (4 urnas)'
    };
    return labels[tipo_capacidade] || 'Desconhecido';
  }
}

module.exports = new OssuarioService();
