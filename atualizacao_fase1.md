# Implementação do Cadastro de Ossuários - Portal Evolution

## Visão Geral da Funcionalidade

A implementação do cadastro de ossuários introduzirá uma nova modalidade de produto no sistema, diferenciando-se dos ETENs pela estrutura de nichos com múltiplas urnas. O sistema criará uma estrutura paralela e segregada para ossuários com hierarquia específica: Cliente → Produto (OSSUÁRIO) → Bloco → Nicho → Urna.

## Terminologia e Conceitos

### Para ETENs (Sistema Atual - Inalterado):
- **Estrutura:** Cliente → Produto → Bloco → Sub-Bloco → Gaveta → Sepultamento
- **Capacidade:** 1 gaveta = 1 sepultamento

### Para Ossuários (Nova Implementação):
- **Estrutura:** Cliente → Produto → Bloco → Nicho → Urna → Sepultamento
- **Terminologia:**
  - **Bloco:** Divisão física do ossuário (mantém o nome "bloco")
  - **Nicho:** Unidade individual dentro do bloco (substitui "gaveta")
  - **Urna:** Espaço específico dentro do nicho (1 a 4 urnas por nicho)
- **Capacidade:** 1 nicho = 1 a 4 urnas (dependendo do tipo do bloco)

## Alterações no Banco de Dados PostgreSQL

### 1. Tabela `produtos` (Inalterada)

**Status:**
- A tabela já possui o campo `tipo` com CHECK constraint para 'ETEN' e 'OSSUARIO'
- Nenhuma alteração estrutural necessária na tabela produtos
- Sistema atual de ETENs permanece totalmente inalterado

### 2. Novas Tabelas Exclusivas para Ossuários

**Justificativa da Segregação:**
- Separação completa entre dados de ETENs e Ossuários
- Evita conflitos e confusões na estrutura de dados
- Permite otimizações específicas para cada tipo de produto
- Facilita manutenção e evolução independente

### 3. Nova Tabela `blocos_ossuarios`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS blocos_ossuarios (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    tipo_capacidade INTEGER NOT NULL CHECK (tipo_capacidade IN (1, 2, 3, 4)),
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco),
    FOREIGN KEY (codigo_cliente, codigo_estacao) REFERENCES produtos(codigo_cliente, codigo_estacao)
);

COMMENT ON TABLE blocos_ossuarios IS 'Blocos específicos para produtos do tipo OSSUARIO';
COMMENT ON COLUMN blocos_ossuarios.tipo_capacidade IS 'Capacidade de urnas por nicho: 1=Simples, 2=Duplo, 3=Triplo, 4=Quádruplo';
```

### 4. Nova Tabela `nichos_ossuarios`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS nichos_ossuarios (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    numero_nicho INTEGER NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco)
        REFERENCES blocos_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco)
);

COMMENT ON TABLE nichos_ossuarios IS 'Nichos individuais dentro dos blocos de ossuários';
COMMENT ON COLUMN nichos_ossuarios.numero_nicho IS 'Número sequencial do nicho dentro do bloco (ex: 1, 2, 3...)';
```

### 5. Nova Tabela `urnas_ossuarios`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS urnas_ossuarios (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    numero_nicho INTEGER NOT NULL,
    letra_urna VARCHAR(1) NOT NULL CHECK (letra_urna IN ('A', 'B', 'C', 'D')),
    disponivel BOOLEAN DEFAULT true,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho)
        REFERENCES nichos_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho)
);

COMMENT ON TABLE urnas_ossuarios IS 'Urnas individuais dentro dos nichos de ossuários';
COMMENT ON COLUMN urnas_ossuarios.letra_urna IS 'Letra da urna dentro do nicho (A, B, C ou D)';
```

### 6. Nova Tabela `ranges_nichos_ossuarios`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS ranges_nichos_ossuarios (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    numero_inicio INTEGER NOT NULL,
    numero_fim INTEGER NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco)
        REFERENCES blocos_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco)
);

COMMENT ON TABLE ranges_nichos_ossuarios IS 'Ranges de numeração para criação automática de nichos';
```

### 7. Nova Tabela `sepultamentos_ossuarios`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS sepultamentos_ossuarios (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    numero_nicho INTEGER NOT NULL,
    letra_urna VARCHAR(1) NOT NULL CHECK (letra_urna IN ('A', 'B', 'C', 'D')),
    nome_sepultado VARCHAR(255) NOT NULL,
    data_sepultamento DATE NOT NULL,
    hora_sepultamento TIME,
    data_remocao TIMESTAMP NULL,
    observacoes TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna)
        REFERENCES urnas_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna)
);

COMMENT ON TABLE sepultamentos_ossuarios IS 'Sepultamentos realizados em ossuários';
COMMENT ON COLUMN sepultamentos_ossuarios.letra_urna IS 'Letra da urna dentro do nicho (A, B, C ou D)';
COMMENT ON COLUMN sepultamentos_ossuarios.data_remocao IS 'Data de remoção do sepultado (substitui exumação)';
```

### 8. Índices para Performance

**Novos Índices para Ossuários:**
```sql
CREATE INDEX idx_urnas_ossuarios_disponibilidade
ON urnas_ossuarios(codigo_cliente, codigo_estacao, disponivel)
WHERE ativo = true;

CREATE INDEX idx_sepultamentos_ossuarios_nicho_urna
ON sepultamentos_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, numero_urna)
WHERE ativo = true;

CREATE INDEX idx_nichos_ossuarios_bloco
ON nichos_ossuarios(codigo_cliente, codigo_estacao, codigo_bloco)
WHERE ativo = true;
```

## Lógica de Negócio e Regras

### 1. Criação de Ranges para Ossuários

**Processo Automatizado:**
- Quando um range é criado para um bloco de ossuário (ex: nichos 1-1000)
- O sistema cria automaticamente os nichos (1000 nichos)
- Para cada nicho, cria as urnas baseadas no `tipo_capacidade` do bloco
- Exemplo: Bloco "Duplo" (tipo_capacidade=2) com range 1-1000 = 2000 urnas totais

**Algoritmo de Criação:**
```
Para cada número de nicho no range (1 a 1000):
    1. Criar nicho na tabela `nichos_ossuarios`
    2. Para cada posição (A até letra correspondente ao tipo_capacidade):
        - Criar urna na tabela `urnas_ossuarios` com letra_urna = letra
        - Marcar urna como disponível = true

Mapeamento de letras por tipo_capacidade:
- Simples (1): Urna A
- Duplo (2): Urnas A, B
- Triplo (3): Urnas A, B, C
- Quádruplo (4): Urnas A, B, C, D
```

**Exemplo Prático:**
- Cliente: ITV_001
- Produto: OSSUARIO_001
- Bloco: BL_001 (Duplo - tipo_capacidade=2)
- Range: Nichos 1-1000
- Resultado: 1000 nichos × 2 urnas = 2000 urnas totais
- Nomenclatura: Nicho 0001 (Urnas A,B), Nicho 0002 (Urnas A,B), etc.

### 2. Regras de Disponibilidade

**Para ETENs (comportamento atual - inalterado):**
- Gaveta disponível = true/false
- Um sepultamento por gaveta
- Tabelas: gavetas, sepultamentos

**Para Ossuários (novo comportamento):**
- Nicho disponível = pelo menos uma urna do nicho está disponível
- Urna disponível = true/false individual
- Múltiplos sepultamentos por nicho (um por urna)
- Tabelas: nichos_ossuarios, urnas_ossuarios, sepultamentos_ossuarios

### 3. Validações Específicas

**Validação de Numeração:**
- Nichos: numeração sequencial crescente dentro do mesmo bloco (1, 2, 3, 4...)
- Urnas: letras fixas por nicho (sempre A, B, C, D conforme tipo_capacidade)
- Não pode haver dois nichos com mesmo número no mesmo bloco

**Validação de Capacidade:**
- Bloco Simples: 1 urna por nicho (urna A)
- Bloco Duplo: 2 urnas por nicho (urnas A e B)
- Bloco Triplo: 3 urnas por nicho (urnas A, B e C)
- Bloco Quádruplo: 4 urnas por nicho (urnas A, B, C e D)

**Validação de Sepultamento:**
- Verificar se a urna específica está disponível
- Verificar se a letra da urna é válida para o tipo de bloco
- Marcar urna como indisponível após sepultamento

**Validação de Remoção vs Deleção:**
- **Remoção:** Marca data_remocao, mantém histórico, libera urna para novo sepultamento
- **Deleção:** Remove permanentemente do banco, apaga todo histórico sem possibilidade de recuperação
- **Aviso obrigatório:** Modal de confirmação explicando a diferença e consequências
- **Auditoria:** Remoções são logadas, deleções também são registradas nos logs do sistema

### 4. Conceitos Específicos para Ossuários

**Diferenças Conceituais em Relação aos ETENs:**
- **Não há exumação:** Ossuários não possuem conceito de exumação temporal
- **Remoção permanente:** Quando removido, não há prazo para retorno
- **Meses para exumar:** Campo não aplicável, valor fixo de 36 meses apenas para compatibilidade
- **Gestão de espaço:** Foco na disponibilidade imediata de urnas após remoção

## Alterações no Backend (Node.js/Express)

### 1. Novos Endpoints da API para Ossuários

**Endpoints para Blocos de Ossuários:**
```
GET /api/ossuarios/blocos?codigo_cliente&codigo_estacao
- Lista blocos específicos de ossuários
- Inclui informação de tipo_capacidade

POST /api/ossuarios/blocos
- Cria novo bloco para ossuário
- Campos: nome, descricao, codigo_bloco, tipo_capacidade

PUT /api/ossuarios/blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco
- Atualiza bloco de ossuário
- Permite alterar nome, descrição (não tipo_capacidade após criação de nichos)

DELETE /api/ossuarios/blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco
- Remove bloco e todos os nichos/urnas associados
```

**Endpoints para Ranges de Nichos:**
```
GET /api/ossuarios/ranges?codigo_cliente&codigo_estacao&codigo_bloco
- Lista ranges de nichos de um bloco específico

POST /api/ossuarios/ranges
- Cria range de nichos para bloco de ossuário
- Gera automaticamente nichos e urnas baseado no tipo_capacidade

DELETE /api/ossuarios/ranges/:id
- Remove range e todos os nichos/urnas associados
```

**Endpoints para Nichos e Urnas:**
```
GET /api/ossuarios/nichos?codigo_cliente&codigo_estacao&codigo_bloco
- Lista nichos de um bloco específico
- Inclui contagem de urnas disponíveis por nicho

GET /api/ossuarios/urnas?codigo_cliente&codigo_estacao&codigo_bloco&numero_nicho
- Lista urnas de um nicho específico
- Filtro por disponibilidade

GET /api/ossuarios/urnas/disponiveis?codigo_cliente&codigo_estacao
- Lista todas as urnas disponíveis para sepultamento
- Agrupadas por bloco e nicho para facilitar seleção
```

**Endpoints para Sepultamentos de Ossuários:**
```
GET /api/ossuarios/sepultamentos?codigo_cliente&codigo_estacao
- Lista sepultamentos de ossuários com filtros
- Inclui informações de bloco, nicho e urna

POST /api/ossuarios/sepultamentos
- Cria sepultamento em ossuário
- Campos: nome, data, codigo_bloco, numero_nicho, letra_urna

PUT /api/ossuarios/sepultamentos/:id
- Atualiza sepultamento de ossuário

DELETE /api/ossuarios/sepultamentos/:id
- DELEÇÃO PERMANENTE: Remove sepultamento e todo histórico
- Exibe aviso sobre perda permanente de dados

PATCH /api/ossuarios/sepultamentos/:id/remover
- REMOÇÃO: Marca data_remocao, mantém histórico, libera urna
- Substitui a funcionalidade de exumação para ossuários
```

### 2. Novos Services para Ossuários

**ossuarioService.js:**
```javascript
// Gestão de blocos
async createBloco(blocoData)
async updateBloco(codigo_cliente, codigo_estacao, codigo_bloco, blocoData)
async deleteBloco(codigo_cliente, codigo_estacao, codigo_bloco)
async listBlocos(codigo_cliente, codigo_estacao)

// Gestão de ranges
async createRange(rangeData)
async deleteRange(rangeId)
async listRanges(codigo_cliente, codigo_estacao, codigo_bloco)

// Gestão de nichos e urnas
async listNichos(codigo_cliente, codigo_estacao, codigo_bloco)
async listUrnas(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho)
async listUrnasDisponiveis(codigo_cliente, codigo_estacao)

// Gestão de sepultamentos
async createSepultamento(sepultamentoData)
async updateSepultamento(id, sepultamentoData)
async removerSepultamento(id, dataRemocao) // Marca data_remocao, mantém histórico
async deleteSepultamento(id) // Remove permanentemente do banco
async listSepultamentos(filtros)
```

### 3. Lógica de Criação de Ranges

**Algoritmo Específico para Ossuários:**
```javascript
async createRangeOssuario(rangeData) {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim } = rangeData;

    // 1. Buscar tipo_capacidade do bloco
    const bloco = await getBlocoOssuario(codigo_cliente, codigo_estacao, codigo_bloco);
    const tipo_capacidade = bloco.tipo_capacidade;

    // 2. Criar range na tabela ranges_nichos_ossuarios
    const range = await createRangeRecord(rangeData);

    // 3. Criar nichos sequenciais
    for (let numero_nicho = numero_inicio; numero_nicho <= numero_fim; numero_nicho++) {
        await createNicho({
            codigo_cliente,
            codigo_estacao,
            codigo_bloco,
            numero_nicho
        });

        // 4. Criar urnas para cada nicho baseado no tipo_capacidade
        const letras_urnas = ['A', 'B', 'C', 'D'];
        for (let i = 0; i < tipo_capacidade; i++) {
            await createUrna({
                codigo_cliente,
                codigo_estacao,
                codigo_bloco,
                numero_nicho,
                letra_urna: letras_urnas[i],
                disponivel: true
            });
        }
    }

    // 5. Retornar estatísticas
    const total_nichos = numero_fim - numero_inicio + 1;
    const total_urnas = total_nichos * tipo_capacidade;

    return {
        range_id: range.id,
        total_nichos,
        total_urnas,
        tipo_capacidade
    };
}
```

## Alterações no Frontend (React)

### 1. Modificação do Componente ProdutoModal

**Novo Campo Tipo de Produto:**
```jsx
<FormControl fullWidth required>
    <InputLabel>Tipo de Produto</InputLabel>
    <Select
        value={formData.tipo}
        onChange={handleChange}
        name="tipo"
    >
        <MenuItem value="ETEN">ETEN (Estação de Tratamento)</MenuItem>
        <MenuItem value="OSSUARIO">Ossuário</MenuItem>
    </Select>
</FormControl>
```

**Validação Condicional:**
- Mostrar campo "Meses para Exumar" apenas para ETENs
- Para Ossuários, usar valor fixo de 36 meses (campo oculto)
- Ossuários não possuem conceito de exumação, apenas remoção
- Adicionar informações sobre a diferença entre os tipos

### 2. Novo Componente BlocoOssuarioModal

**Campos Específicos para Blocos de Ossuários:**
```jsx
<TextField
    fullWidth
    required
    label="Nome do Bloco"
    value={formData.nome}
    onChange={handleChange}
    name="nome"
    placeholder="Ex: BLOCO 01"
/>

<TextField
    fullWidth
    label="Descrição"
    value={formData.descricao}
    onChange={handleChange}
    name="descricao"
    multiline
    rows={3}
    placeholder="Descrição detalhada do bloco..."
/>

<TextField
    fullWidth
    required
    label="Código do Bloco"
    value={formData.codigo_bloco}
    onChange={handleChange}
    name="codigo_bloco"
    placeholder="Ex: BL_001"
    disabled={isEditing} // Não permite alterar código em edição
/>

<FormControl fullWidth required>
    <InputLabel>Tipo de Capacidade</InputLabel>
    <Select
        value={formData.tipo_capacidade}
        onChange={handleChange}
        name="tipo_capacidade"
        disabled={hasNichos} // Não permite alterar se já tem nichos criados
    >
        <MenuItem value={1}>Simples (1 urna por nicho)</MenuItem>
        <MenuItem value={2}>Duplo (2 urnas por nicho)</MenuItem>
        <MenuItem value={3}>Triplo (3 urnas por nicho)</MenuItem>
        <MenuItem value={4}>Quádruplo (4 urnas por nicho)</MenuItem>
    </Select>
</FormControl>
```

**Informações Contextuais:**
- Mostrar aviso sobre impacto na criação de urnas
- Calcular total de urnas que serão criadas baseado nos ranges existentes
- Alertar sobre impossibilidade de alterar tipo_capacidade após criação de nichos

### 3. Novo Componente RangeNichosManager

**Gerenciamento Específico para Ranges de Nichos:**
```jsx
<Paper sx={{ p: 3, mb: 3 }}>
    <Typography variant="h6" gutterBottom>
        Adicionar Range de Nichos
    </Typography>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Bloco {bloco.nome} - Tipo: {getTipoCapacidadeLabel(bloco.tipo_capacidade)}
    </Typography>

    <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
        <TextField
            label="Nicho Início"
            type="number"
            value={formData.numero_inicio}
            onChange={(e) => setFormData({ ...formData, numero_inicio: e.target.value })}
            size="small"
            inputProps={{ min: 1 }}
            required
        />
        <TextField
            label="Nicho Fim"
            type="number"
            value={formData.numero_fim}
            onChange={(e) => setFormData({ ...formData, numero_fim: e.target.value })}
            size="small"
            inputProps={{ min: 1 }}
            required
        />
        <Button
            type="submit"
            variant="contained"
            disabled={!isFormValid() || loading}
        >
            {loading ? 'Criando...' : 'Criar Range'}
        </Button>
        <Box sx={{ minWidth: 300 }}>
            <Typography variant="body2" color={isFormValid() ? 'success.main' : 'text.secondary'}>
                {getValidationMessage()}
            </Typography>
        </Box>
    </Box>
</Paper>
```

**Exibição de Ranges Existentes:**
```jsx
// Para Ossuários
<Typography>
    Range {range.numero_inicio}-{range.numero_fim}:
    {range.numero_fim - range.numero_inicio + 1} nichos × {bloco.tipo_capacidade} urnas =
    {(range.numero_fim - range.numero_inicio + 1) * bloco.tipo_capacidade} urnas totais
</Typography>
```

### 4. Modificação da Página Book de Sepultamentos

**Exibição Unificada de ETENs e Ossuários:**
```jsx
{produtos.map(produto => (
    <Card key={`${produto.codigo_cliente}-${produto.codigo_estacao}`}>
        <CardContent>
            <Typography variant="h6">
                {produto.denominacao}
                <Chip
                    label={produto.tipo}
                    size="small"
                    color={produto.tipo === 'ETEN' ? 'primary' : 'secondary'}
                    sx={{ ml: 1 }}
                />
            </Typography>
            <Typography variant="body2" color="text.secondary">
                {produto.tipo === 'ETEN' ? 'Estação de Tratamento' : 'Ossuário'}
            </Typography>
            <Button
                variant="contained"
                onClick={() => handleViewProduct(produto)}
                sx={{ mt: 2 }}
            >
                Ver Sepultamentos
            </Button>
        </CardContent>
    </Card>
))}
```

### 5. Novo Componente SepultamentoOssuarioModal

**Formulário Específico para Sepultamentos em Ossuários:**
```jsx
<TextField
    fullWidth
    required
    label="Nome do Sepultado"
    value={formData.nome_sepultado}
    onChange={handleChange}
    name="nome_sepultado"
/>

<TextField
    fullWidth
    required
    type="date"
    label="Data de Sepultamento"
    value={formData.data_sepultamento}
    onChange={handleChange}
    name="data_sepultamento"
    InputLabelProps={{ shrink: true }}
/>

<FormControl fullWidth required>
    <InputLabel>Bloco</InputLabel>
    <Select
        value={formData.codigo_bloco}
        onChange={handleBlocoChange}
        name="codigo_bloco"
    >
        {blocosDisponiveis.map(bloco => (
            <MenuItem key={bloco.codigo_bloco} value={bloco.codigo_bloco}>
                {bloco.nome} ({bloco.tipo_capacidade === 1 ? 'Simples' :
                             bloco.tipo_capacidade === 2 ? 'Duplo' :
                             bloco.tipo_capacidade === 3 ? 'Triplo' : 'Quádruplo'})
            </MenuItem>
        ))}
    </Select>
</FormControl>

<FormControl fullWidth required>
    <InputLabel>Nicho</InputLabel>
    <Select
        value={formData.numero_nicho}
        onChange={handleNichoChange}
        name="numero_nicho"
        disabled={!formData.codigo_bloco}
    >
        {nichosDisponiveis.map(nicho => (
            <MenuItem key={nicho.numero_nicho} value={nicho.numero_nicho}>
                Nicho {nicho.numero_nicho.toString().padStart(4, '0')}
                ({nicho.urnas_disponiveis}/{nicho.urnas_totais} urnas disponíveis)
            </MenuItem>
        ))}
    </Select>
</FormControl>

<FormControl fullWidth required>
    <InputLabel>Urna</InputLabel>
    <Select
        value={formData.letra_urna}
        onChange={handleChange}
        name="letra_urna"
        disabled={!formData.numero_nicho}
    >
        {urnasDisponiveis.map(urna => (
            <MenuItem key={urna.letra_urna} value={urna.letra_urna}>
                Urna {urna.letra_urna}
            </MenuItem>
        ))}
    </Select>
</FormControl>

{/* Botões de Ação com Diferenciação */}
<Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
    <Button variant="outlined" onClick={onClose}>
        Cancelar
    </Button>
    <Button
        variant="contained"
        color="warning"
        onClick={handleRemover}
        disabled={!sepultamento}
    >
        Remover (Manter Histórico)
    </Button>
    <Button
        variant="contained"
        color="error"
        onClick={handleDeletar}
        disabled={!sepultamento}
    >
        Deletar (Apagar Histórico)
    </Button>
    <Button
        variant="contained"
        type="submit"
        disabled={loading}
    >
        {loading ? 'Salvando...' : 'Salvar Sepultamento'}
    </Button>
</Box>

<TextField
    fullWidth
    multiline
    rows={3}
    label="Observações"
    value={formData.observacoes}
    onChange={handleChange}
    name="observacoes"
    placeholder="Observações sobre o sepultamento..."
/>
```

### 4. Novo Componente SepultamentoModal (Ossuários)

**Seleção de Urna:**
```jsx
{produtoTipo === 'OSSUARIO' && (
    <>
        <FormControl fullWidth required>
            <InputLabel>Gaveta</InputLabel>
            <Select value={selectedGaveta} onChange={handleGavetaChange}>
                {gavetasDisponiveis.map(gaveta => (
                    <MenuItem key={gaveta.numero} value={gaveta.numero}>
                        Gaveta {gaveta.numero} ({gaveta.urnas_disponiveis}/{gaveta.urnas_totais} urnas disponíveis)
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
        
        <FormControl fullWidth required>
            <InputLabel>Posição da Urna</InputLabel>
            <Select value={selectedUrna} onChange={handleUrnaChange}>
                {urnasDisponiveis.map(urna => (
                    <MenuItem key={urna.posicao} value={urna.posicao}>
                        Posição {urna.posicao}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    </>
)}
```

### 5. Modificação do Dashboard

**Métricas Diferenciadas:**
```jsx
// Para ETENs
<Card>
    <Typography>Gavetas Ocupadas: {gavetasOcupadas}</Typography>
    <Typography>Gavetas Disponíveis: {gavetasDisponiveis}</Typography>
</Card>

// Para Ossuários
<Card>
    <Typography>Urnas Ocupadas: {urnasOcupadas}</Typography>
    <Typography>Urnas Disponíveis: {urnasDisponiveis}</Typography>
    <Typography>Gavetas com Vagas: {gavetasComVagas}</Typography>
    <Typography>Gavetas Lotadas: {gavetasLotadas}</Typography>
</Card>
```

## Fluxo de Trabalho Completo

### 1. Cadastro de Produto Ossuário
1. Usuário acessa "Cadastro de Produtos" → "Adicionar Novo Produto"
2. Seleciona tipo "OSSUÁRIO"
3. Preenche código da estação (ex: "OSSUARIO_001")
4. Preenche denominação e observações
5. Sistema cria produto com tipo='OSSUARIO' na tabela produtos

### 2. Cadastro de Bloco para Ossuário
1. Usuário acessa produto ossuário criado
2. Clica em "Novo Bloco" (interface específica para ossuários)
3. Preenche:
   - Nome (ex: "BLOCO 01")
   - Descrição
   - Código do Bloco (ex: "BL_001")
   - Tipo: Simples/Duplo/Triplo/Quádruplo
4. Sistema cria registro na tabela blocos_ossuarios
5. Chave única: codigo_cliente + codigo_estacao + codigo_bloco

### 3. Criação de Range de Nichos
1. Usuário acessa bloco criado
2. Clica em "Range de Nichos"
3. Define numeração início e fim (ex: 1 até 1000)
4. Sistema valida conflitos de numeração no mesmo bloco
5. Sistema cria:
   - 1000 registros na tabela nichos_ossuarios
   - 2000 registros na tabela urnas_ossuarios (para bloco Duplo)
   - Nomenclatura: Nicho 1 (Urnas A,B), Nicho 2 (Urnas A,B), etc.

### 4. Visualização no Book de Sepultamentos
1. Aba "Book de Sepultamentos" mostra produtos ETEN e OSSUARIO
2. Produtos ossuários são identificados com chip/badge específico
3. Ao clicar em produto OSSUARIO, lista sepultamentos da tabela sepultamentos_ossuarios do respectivo produto clicado (Exemplo: "codigo_estacao" = "OSSUARIO_001")

### 5. Registro de Sepultamento em Ossuário
1. Usuário seleciona produto OSSUARIO
2. Clica em "Novo Sepultamento"
3. Preenche formulário específico:
   - Nome do sepultado
   - Data de sepultamento
   - Localização (Bloco): lista blocos disponíveis
   - Nicho: lista nichos com urnas disponíveis (ex: "Nicho 0001 (1/2 urnas disponíveis)")
   - Urna: lista urnas disponíveis do nicho selecionado (A, B, C ou D)
4. Sistema cria registro na tabela sepultamentos_ossuarios
5. Sistema marca urna específica como indisponível

### 6. Gestão de Remoção vs Deleção
1. **Remoção:** Usuário clica em "Remover" → informa data de remoção → mantém histórico
2. **Deleção:** Usuário clica em "Deletar" → aviso sobre perda permanente → confirma → apaga registro
3. **Diferença:** Remoção preserva dados para auditoria, deleção remove completamente

### 7. Exemplo Prático Completo
**Cenário:** Cliente ITV_001 cadastra ossuário
1. **Produto:** OSSUARIO_001 (tipo='OSSUARIO')
2. **Bloco:** BL_001 (nome="BLOCO 01", tipo_capacidade=2 - Duplo)
3. **Range:** Nichos 1-1000
4. **Resultado:** 1000 nichos × 2 urnas = 2000 urnas totais
5. **Sepultamento:** Bloco BL_001, Nicho 0001, Urna A
6. **Localização completa:** ITV_001/OSSUARIO_001/BL_001/Nicho 0001/Urna A

## Considerações de Performance

### 1. Impacto na Criação de Ranges
- Range 1-1000 em bloco Quádruplo = 4000 registros na tabela urnas_ossuarios
- Implementar criação em lotes (batch insert) para otimização
- Mostrar barra de progresso para ranges grandes
- Validar limites máximos por range (ex: máximo 1000 nichos por vez)

### 2. Consultas Otimizadas
- Índices específicos para consultas de disponibilidade de urnas
- Views materializadas para estatísticas de ocupação por bloco
- Cache de contadores de urnas disponíveis por nicho
- Consultas paginadas para listagens grandes

### 3. Validações de Integridade
- Triggers para manter consistência nicho ↔ urnas
- Validação de tipo_capacidade vs quantidade de urnas criadas
- Prevenção de alteração de tipo_capacidade após criação de nichos
- Validação de numeração sequencial de nichos

### 4. Segregação de Dados
- Tabelas completamente separadas entre ETENs e Ossuários
- Queries específicas para cada tipo de produto
- Evita joins desnecessários entre sistemas diferentes
- Facilita manutenção e evolução independente

## Migração e Compatibilidade

### 1. Sistema Atual (ETENs) - Totalmente Preservado
- Nenhuma alteração nas tabelas existentes de ETENs
- Comportamento atual mantido 100% inalterado
- Usuários não percebem diferença no uso de ETENs
- Todas as funcionalidades atuais continuam funcionando

### 2. Implementação Incremental
- Ossuários são adicionados como funcionalidade nova
- Interface unificada mostra ambos os tipos
- Usuários podem usar ETENs e Ossuários simultaneamente
- Migração gradual conforme necessidade do cliente

### 3. Dados Existentes
- Produtos existentes permanecem como tipo='ETEN'
- Nenhuma conversão ou migração de dados necessária
- Sistema funciona imediatamente após implementação
- Novos produtos podem ser criados como OSSUARIO

Esta implementação cria um sistema dual robusto que mantém total compatibilidade com ETENs existentes enquanto introduz a funcionalidade completa de ossuários com estrutura hierárquica de nichos e urnas, permitindo controle granular de cada espaço individual dentro dos ossuários.
