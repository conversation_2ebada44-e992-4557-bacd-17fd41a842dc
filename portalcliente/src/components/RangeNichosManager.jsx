import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Delete as DeleteIcon } from '@mui/icons-material';
import { StandardButton } from './common';

const RangeNichosManager = ({ bloco, produto, onRangeChange }) => {
  const [formData, setFormData] = useState({
    numero_inicio: '',
    numero_fim: ''
  });
  const [ranges, setRanges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    if (bloco) {
      loadRanges();
    }
  }, [bloco]);

  const loadRanges = async () => {
    try {
      const response = await fetch(`/api/ossuarios/ranges?codigo_cliente=${produto.codigo_cliente}&codigo_estacao=${produto.codigo_estacao}&codigo_bloco=${bloco.codigo_bloco}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar ranges');
      }

      const data = await response.json();
      setRanges(data);
    } catch (error) {
      console.error('Erro ao carregar ranges:', error);
      setError('Erro ao carregar ranges de nichos');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: parseInt(value) || ''
    }));
  };

  const getTipoCapacidadeLabel = (tipo) => {
    const labels = {
      1: 'Simples',
      2: 'Duplo',
      3: 'Triplo',
      4: 'Quádruplo'
    };
    return labels[tipo] || 'Desconhecido';
  };

  const getValidationMessage = () => {
    if (!formData.numero_inicio || !formData.numero_fim) {
      return 'Preencha os números de início e fim';
    }

    if (formData.numero_inicio > formData.numero_fim) {
      return 'Número de início deve ser menor ou igual ao fim';
    }

    if (formData.numero_inicio < 1) {
      return 'Número de início deve ser maior que zero';
    }

    // Verificar sobreposição com ranges existentes
    const hasOverlap = ranges.some(range => {
      return (
        (formData.numero_inicio <= range.numero_fim && formData.numero_fim >= range.numero_inicio)
      );
    });

    if (hasOverlap) {
      return 'Range sobrepõe com range existente';
    }

    const totalNichos = formData.numero_fim - formData.numero_inicio + 1;
    const totalUrnas = totalNichos * bloco.tipo_capacidade;

    return `✅ Criará ${totalNichos} nichos × ${bloco.tipo_capacidade} urnas = ${totalUrnas} urnas totais`;
  };

  const isFormValid = () => {
    if (!formData.numero_inicio || !formData.numero_fim) return false;
    if (formData.numero_inicio > formData.numero_fim) return false;
    if (formData.numero_inicio < 1) return false;

    // Verificar sobreposição
    const hasOverlap = ranges.some(range => {
      return (
        (formData.numero_inicio <= range.numero_fim && formData.numero_fim >= range.numero_inicio)
      );
    });

    return !hasOverlap;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!isFormValid()) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/ossuarios/ranges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          codigo_cliente: produto.codigo_cliente,
          codigo_estacao: produto.codigo_estacao,
          codigo_bloco: bloco.codigo_bloco,
          numero_inicio: formData.numero_inicio,
          numero_fim: formData.numero_fim
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao criar range');
      }

      const result = await response.json();
      console.log('✅ Range criado com sucesso:', result);

      setSuccess(`Range criado! ${result.total_nichos} nichos e ${result.total_urnas} urnas foram criados.`);
      setFormData({ numero_inicio: '', numero_fim: '' });
      
      // Recarregar ranges
      await loadRanges();
      
      // Notificar componente pai
      if (onRangeChange) {
        onRangeChange();
      }

    } catch (error) {
      console.error('❌ Erro ao criar range:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRange = async (rangeId) => {
    if (!confirm('Tem certeza que deseja deletar este range? Todos os nichos e urnas associados serão removidos.')) {
      return;
    }

    try {
      const response = await fetch(`/api/ossuarios/ranges/${rangeId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao deletar range');
      }

      setSuccess('Range deletado com sucesso!');
      
      // Recarregar ranges
      await loadRanges();
      
      // Notificar componente pai
      if (onRangeChange) {
        onRangeChange();
      }

    } catch (error) {
      console.error('❌ Erro ao deletar range:', error);
      setError(error.message);
    }
  };

  if (!bloco) {
    return (
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" color="text.secondary">
          Selecione um bloco para gerenciar ranges de nichos
        </Typography>
      </Paper>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      
      {/* Informações do Bloco */}
      <Paper sx={{ p: 3, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
        <Typography variant="h6" gutterBottom>
          📦 {bloco.nome}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Bloco {bloco.codigo_bloco} - Tipo: {getTipoCapacidadeLabel(bloco.tipo_capacidade)}
          <br />
          Nichos: {bloco.total_nichos} | Urnas: {bloco.total_urnas} | Disponíveis: {bloco.urnas_disponiveis}
        </Typography>
      </Paper>

      {/* Formulário para Adicionar Range */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Adicionar Range de Nichos
        </Typography>
        
        <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', flexWrap: 'wrap' }}>
          <TextField
            label="Nicho Início"
            type="number"
            name="numero_inicio"
            value={formData.numero_inicio}
            onChange={handleChange}
            size="small"
            inputProps={{ min: 1 }}
            required
            sx={{ minWidth: 120 }}
          />
          <TextField
            label="Nicho Fim"
            type="number"
            name="numero_fim"
            value={formData.numero_fim}
            onChange={handleChange}
            size="small"
            inputProps={{ min: 1 }}
            required
            sx={{ minWidth: 120 }}
          />
          <StandardButton
            type="submit"
            variant="contained"
            disabled={!isFormValid() || loading}
            sx={{ minHeight: 40 }}
          >
            {loading ? 'Criando...' : 'Criar Range'}
          </StandardButton>
        </Box>

        <Box sx={{ mt: 2, minHeight: 40 }}>
          <Typography variant="body2" color={isFormValid() ? 'success.main' : 'text.secondary'}>
            {getValidationMessage()}
          </Typography>
        </Box>
      </Paper>

      {/* Lista de Ranges Existentes */}
      {ranges.length > 0 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Ranges Existentes
          </Typography>
          
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Range</TableCell>
                  <TableCell>Nichos</TableCell>
                  <TableCell>Urnas Totais</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell align="center">Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {ranges.map((range) => (
                  <TableRow key={range.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {range.numero_inicio} - {range.numero_fim}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={`${range.total_nichos} nichos`} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={`${range.total_urnas} urnas`} 
                        size="small" 
                        color="secondary" 
                        variant="outlined" 
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {getTipoCapacidadeLabel(range.tipo_capacidade)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Deletar Range">
                        <IconButton 
                          color="error" 
                          size="small"
                          onClick={() => handleDeleteRange(range.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Mensagens de Feedback */}
      {error && (
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

    </Box>
  );
};

export default RangeNichosManager;
