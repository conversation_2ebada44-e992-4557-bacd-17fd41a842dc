const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const path = require('path');
require('dotenv').config();

const { initializeDatabase } = require('./database/connection');
const { loggingMiddleware } = require('./utils/logger');

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'portal-evolution-secret-key';

// Middlewares
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Servir arquivos estáticos de upload com configurações de segurança
app.use('/api/uploads', (req, res, next) => {
  // Verificar se é uma requisição para logo
  if (req.path.startsWith('/logos/')) {
    // Configurar headers de cache para logos
    res.set({
      'Cache-Control': 'public, max-age=86400', // Cache por 24 horas
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block'
    });

    // Verificar extensão do arquivo
    const allowedExtensions = ['.png', '.jpg', '.jpeg'];
    const fileExtension = path.extname(req.path).toLowerCase();

    if (!allowedExtensions.includes(fileExtension)) {
      return res.status(403).json({ error: 'Tipo de arquivo não permitido' });
    }
  }

  next();
}, express.static(path.join(__dirname, 'uploads')));

// Middleware de logging básico
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Middleware de logging automático para auditoria
app.use(loggingMiddleware);

// Middleware de autenticação
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  console.log(`🔐 Middleware Auth - ${req.method} ${req.path}:`, {
    hasAuthHeader: !!authHeader,
    hasToken: !!token,
    tokenLength: token?.length,
    userAgent: req.get('User-Agent')?.substring(0, 50)
  });

  if (!token) {
    console.log('❌ Token não fornecido');
    return res.status(401).json({ error: 'Token de acesso requerido' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log('❌ Token inválido:', {
        error: err.message,
        name: err.name,
        tokenPreview: token.substring(0, 50) + '...',
        jwtSecret: JWT_SECRET.substring(0, 10) + '...'
      });
      return res.status(403).json({ error: 'Token inválido' });
    }

    console.log(`✅ Token válido para usuário: ${user.email} (${user.tipo_usuario})`);
    req.user = user;
    next();
  });
};

// Middleware para verificar se é admin
const requireAdmin = (req, res, next) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }
  next();
};


// Rotas de autenticação
app.use('/api/auth', require('./routes/auth'));

// Rotas protegidas - NOVA ESTRUTURA POR CÓDIGOS
app.use('/api/usuarios', authenticateToken, require('./routes/usuarios'));
app.use('/api/clientes', authenticateToken, require('./routes/clientes'));
app.use('/api/produtos', authenticateToken, require('./routes/produtos_new')); // Nova estrutura por códigos
app.use('/api/sub-blocos', authenticateToken, require('./routes/sub_blocos_new')); // Nova estrutura por códigos
app.use('/api/sepultamentos', authenticateToken, require('./routes/sepultamentos_new')); // Nova estrutura por códigos
app.use('/api/gavetas', authenticateToken, require('./routes/gavetas'));
app.use('/api/ranges', authenticateToken, require('./routes/ranges')); // Gerenciamento de ranges de gavetas
app.use('/api/ossuarios', authenticateToken, require('./routes/ossuarios')); // Gestão de ossuários
app.use('/api/transferencia-gavetas', authenticateToken, require('./routes/transferencia_gavetas')); // Transferência de gavetas
app.use('/api/logs', authenticateToken, requireAdmin, require('./routes/logs'));
app.use('/api/dashboard', authenticateToken, require('./routes/dashboard_new')); // Nova estrutura por códigos
app.use('/api/relatorios', authenticateToken, require('./routes/relatorios')); // Relatórios
app.use('/api/relatorio-mensal', authenticateToken, require('./routes/relatorio_mensal')); // Relatório Mensal
// app.use('/api/relatorio-mensal-corrigido', authenticateToken, require('./routes/relatorio_mensal_corrigido')); // Relatório Mensal Corrigido - Temporariamente desabilitado

// Rota de teste
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Portal Evolution API está funcionando',
    timestamp: new Date().toISOString()
  });
});



// Rota para webhook
app.post('/api/webhook', (req, res) => {
  console.log('📨 Webhook recebido:', req.body);
  res.json({ status: 'received' });
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error('❌ Erro no servidor:', err);
  res.status(500).json({ 
    error: 'Erro interno do servidor',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Algo deu errado'
  });
});

// Rota 404
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Rota não encontrada' });
});

// Inicializar servidor
const startServer = async () => {
  try {
    // Inicializar banco de dados
    const dbInitialized = await initializeDatabase();
    
    if (!dbInitialized) {
      console.error('❌ Falha ao inicializar banco de dados');
      process.exit(1);
    }

    // Iniciar servidor
    app.listen(PORT, () => {
      console.log(`🚀 Servidor Portal Evolution rodando na porta ${PORT}`);
      console.log(`📍 API disponível em: http://localhost:${PORT}/api`);
      console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error('❌ Erro ao iniciar servidor:', error);
    process.exit(1);
  }
};

startServer();
