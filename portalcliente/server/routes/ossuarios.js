const express = require('express');
const router = express.Router();
const ossuarioService = require('../services/ossuarioService');
const { query } = require('../database/connection');

// Nota: O middleware de autenticação é aplicado no index.js antes de chamar esta rota
// Todas as rotas aqui já estão protegidas pelo authenticateToken

// ==================== ROTAS PARA BLOCOS DE OSSUÁRIOS ====================

// Listar blocos de um ossuário
router.get('/blocos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao } = req.query;
    
    if (!codigo_cliente || !codigo_estacao) {
      return res.status(400).json({ error: 'Código do cliente e código da estação são obrigatórios' });
    }
    
    // Verificar se é cliente e se tem acesso ao produto
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    // Verificar se o produto é do tipo OSSUARIO
    const produto = await query(
      'SELECT tipo FROM produtos WHERE codigo_cliente = $1 AND codigo_estacao = $2',
      [codigo_cliente, codigo_estacao]
    );
    
    if (produto.rows.length === 0) {
      return res.status(404).json({ error: 'Produto não encontrado' });
    }
    
    if (produto.rows[0].tipo !== 'OSSUARIO') {
      return res.status(400).json({ error: 'Este produto não é um ossuário' });
    }
    
    const blocos = await ossuarioService.listBlocos(codigo_cliente, codigo_estacao);
    res.json(blocos);
  } catch (error) {
    console.error('Erro ao listar blocos de ossuário:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo bloco para ossuário
router.post('/blocos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, nome, descricao, tipo_capacidade } = req.body;
    
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !nome || !tipo_capacidade) {
      return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }
    
    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const bloco = await ossuarioService.createBloco({
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      nome,
      descricao,
      tipo_capacidade
    });
    
    res.status(201).json(bloco);
  } catch (error) {
    console.error('Erro ao criar bloco de ossuário:', error);
    if (error.message.includes('já existe') || error.message.includes('não encontrado') || error.message.includes('não é um ossuário')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Atualizar bloco de ossuário
router.put('/blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.params;
    const { nome, descricao } = req.body;
    
    if (!nome) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }
    
    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const bloco = await ossuarioService.updateBloco(codigo_cliente, codigo_estacao, codigo_bloco, {
      nome,
      descricao
    });
    
    res.json(bloco);
  } catch (error) {
    console.error('Erro ao atualizar bloco de ossuário:', error);
    if (error.message.includes('não encontrado')) {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Deletar bloco de ossuário
router.delete('/blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.params;
    
    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const bloco = await ossuarioService.deleteBloco(codigo_cliente, codigo_estacao, codigo_bloco);
    res.json({ message: 'Bloco deletado com sucesso', bloco });
  } catch (error) {
    console.error('Erro ao deletar bloco de ossuário:', error);
    if (error.message.includes('sepultamentos ativos')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// ==================== ROTAS PARA RANGES DE NICHOS ====================

// Listar ranges de um bloco
router.get('/ranges', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.query;
    
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco) {
      return res.status(400).json({ error: 'Código do cliente, estação e bloco são obrigatórios' });
    }
    
    // Verificar se é cliente e se tem acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const ranges = await ossuarioService.listRanges(codigo_cliente, codigo_estacao, codigo_bloco);
    res.json(ranges);
  } catch (error) {
    console.error('Erro ao listar ranges de nichos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar range de nichos
router.post('/ranges', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_inicio, numero_fim } = req.body;
    
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !numero_inicio || !numero_fim) {
      return res.status(400).json({ error: 'Todos os campos são obrigatórios' });
    }
    
    if (numero_inicio > numero_fim) {
      return res.status(400).json({ error: 'Número de início deve ser menor ou igual ao número fim' });
    }
    
    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const result = await ossuarioService.createRange({
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      numero_inicio,
      numero_fim
    });
    
    res.status(201).json(result);
  } catch (error) {
    console.error('Erro ao criar range de nichos:', error);
    if (error.message.includes('sobrepõe') || error.message.includes('não encontrado')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Deletar range de nichos
router.delete('/ranges/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Buscar dados do range para verificar acesso
    const rangeData = await query('SELECT codigo_cliente FROM ranges_nichos_ossuarios WHERE id = $1', [id]);
    
    if (rangeData.rows.length === 0) {
      return res.status(404).json({ error: 'Range não encontrado' });
    }
    
    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== rangeData.rows[0].codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const range = await ossuarioService.deleteRange(id);
    res.json({ message: 'Range deletado com sucesso', range });
  } catch (error) {
    console.error('Erro ao deletar range de nichos:', error);
    if (error.message.includes('sepultamentos ativos') || error.message.includes('não encontrado')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// ==================== ROTAS PARA NICHOS E URNAS ====================

// Listar nichos de um bloco
router.get('/nichos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco } = req.query;
    
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco) {
      return res.status(400).json({ error: 'Código do cliente, estação e bloco são obrigatórios' });
    }
    
    // Verificar se é cliente e se tem acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const nichos = await ossuarioService.listNichos(codigo_cliente, codigo_estacao, codigo_bloco);
    res.json(nichos);
  } catch (error) {
    console.error('Erro ao listar nichos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar urnas de um nicho
router.get('/urnas', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho } = req.query;
    
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !numero_nicho) {
      return res.status(400).json({ error: 'Todos os parâmetros são obrigatórios' });
    }
    
    // Verificar se é cliente e se tem acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const urnas = await ossuarioService.listUrnas(codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho);
    res.json(urnas);
  } catch (error) {
    console.error('Erro ao listar urnas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar urnas disponíveis para sepultamento
router.get('/urnas/disponiveis', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao } = req.query;
    
    if (!codigo_cliente || !codigo_estacao) {
      return res.status(400).json({ error: 'Código do cliente e estação são obrigatórios' });
    }
    
    // Verificar se é cliente e se tem acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    const urnas = await ossuarioService.listUrnasDisponiveis(codigo_cliente, codigo_estacao);
    res.json(urnas);
  } catch (error) {
    console.error('Erro ao listar urnas disponíveis:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ==================== ROTAS PARA SEPULTAMENTOS ====================

// Listar sepultamentos de ossuários
router.get('/sepultamentos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, incluir_removidos, limit, offset } = req.query;

    if (!codigo_cliente) {
      return res.status(400).json({ error: 'Código do cliente é obrigatório' });
    }

    // Verificar se é cliente e se tem acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const sepultamentos = await ossuarioService.listSepultamentos({
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      incluir_removidos: incluir_removidos === 'true',
      limit: parseInt(limit) || 50,
      offset: parseInt(offset) || 0
    });

    res.json(sepultamentos);
  } catch (error) {
    console.error('Erro ao listar sepultamentos de ossuários:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar sepultamento em ossuário
router.post('/sepultamentos', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, numero_nicho, letra_urna, nome_sepultado, data_sepultamento, hora_sepultamento, observacoes } = req.body;

    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !numero_nicho || !letra_urna || !nome_sepultado || !data_sepultamento) {
      return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }

    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const sepultamento = await ossuarioService.createSepultamento({
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      numero_nicho,
      letra_urna,
      nome_sepultado,
      data_sepultamento,
      hora_sepultamento,
      observacoes
    });

    res.status(201).json(sepultamento);
  } catch (error) {
    console.error('Erro ao criar sepultamento em ossuário:', error);
    if (error.message.includes('não encontrada') || error.message.includes('não está disponível') || error.message.includes('já existe')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Atualizar sepultamento em ossuário
router.put('/sepultamentos/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { nome_sepultado, data_sepultamento, hora_sepultamento, observacoes } = req.body;

    if (!nome_sepultado || !data_sepultamento) {
      return res.status(400).json({ error: 'Nome do sepultado e data são obrigatórios' });
    }

    // Buscar dados do sepultamento para verificar acesso
    const sepultamentoData = await query('SELECT codigo_cliente FROM sepultamentos_ossuarios WHERE id = $1', [id]);

    if (sepultamentoData.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== sepultamentoData.rows[0].codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const sepultamento = await ossuarioService.updateSepultamento(id, {
      nome_sepultado,
      data_sepultamento,
      hora_sepultamento,
      observacoes
    });

    res.json(sepultamento);
  } catch (error) {
    console.error('Erro ao atualizar sepultamento em ossuário:', error);
    if (error.message.includes('não encontrado')) {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Remover sepultamento (mantém histórico)
router.patch('/sepultamentos/:id/remover', async (req, res) => {
  try {
    const { id } = req.params;
    const { data_remocao } = req.body;

    if (!data_remocao) {
      return res.status(400).json({ error: 'Data de remoção é obrigatória' });
    }

    // Buscar dados do sepultamento para verificar acesso
    const sepultamentoData = await query('SELECT codigo_cliente FROM sepultamentos_ossuarios WHERE id = $1', [id]);

    if (sepultamentoData.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== sepultamentoData.rows[0].codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const sepultamento = await ossuarioService.removerSepultamento(id, data_remocao);
    res.json({ message: 'Sepultamento removido com sucesso (histórico mantido)', sepultamento });
  } catch (error) {
    console.error('Erro ao remover sepultamento em ossuário:', error);
    if (error.message.includes('não encontrado')) {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

// Deletar sepultamento permanentemente
router.delete('/sepultamentos/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar dados do sepultamento para verificar acesso
    const sepultamentoData = await query('SELECT codigo_cliente FROM sepultamentos_ossuarios WHERE id = $1', [id]);

    if (sepultamentoData.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    // Verificar se é admin ou cliente com acesso
    if (req.user.tipo_usuario === 'cliente' && req.user.codigo_cliente !== sepultamentoData.rows[0].codigo_cliente) {
      return res.status(403).json({ error: 'Acesso negado' });
    }

    const sepultamento = await ossuarioService.deleteSepultamento(id);
    res.json({ message: 'Sepultamento deletado permanentemente', sepultamento });
  } catch (error) {
    console.error('Erro ao deletar sepultamento em ossuário:', error);
    if (error.message.includes('não encontrado')) {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Erro interno do servidor' });
    }
  }
});

module.exports = router;
