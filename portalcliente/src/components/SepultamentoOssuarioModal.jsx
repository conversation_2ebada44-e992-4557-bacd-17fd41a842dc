import React, { useState, useEffect } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Alert,
  Typography,
  Paper,
  Chip,
  Divider,
} from '@mui/material';
import Modal from './Modal';
import { StandardButton } from './common';

const SepultamentoOssuarioModal = ({ isOpen, onClose, sepultamento = null, produto, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_bloco: '',
    numero_nicho: '',
    letra_urna: '',
    nome_sepultado: '',
    data_sepultamento: '',
    hora_sepultamento: '',
    observacoes: ''
  });
  const [blocos, setBlocos] = useState([]);
  const [nichosDisponiveis, setNichosDisponiveis] = useState([]);
  const [urnasDisponiveis, setUrnasDisponiveis] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && produto) {
      loadBlocos();
      if (sepultamento) {
        setFormData({
          codigo_bloco: sepultamento.codigo_bloco || '',
          numero_nicho: sepultamento.numero_nicho || '',
          letra_urna: sepultamento.letra_urna || '',
          nome_sepultado: sepultamento.nome_sepultado || '',
          data_sepultamento: sepultamento.data_sepultamento || '',
          hora_sepultamento: sepultamento.hora_sepultamento || '',
          observacoes: sepultamento.observacoes || ''
        });
      } else {
        setFormData({
          codigo_bloco: '',
          numero_nicho: '',
          letra_urna: '',
          nome_sepultado: '',
          data_sepultamento: new Date().toISOString().split('T')[0],
          hora_sepultamento: new Date().toTimeString().slice(0, 5),
          observacoes: ''
        });
      }
      setError('');
    }
  }, [isOpen, produto, sepultamento]);

  const loadBlocos = async () => {
    try {
      const response = await fetch(`/api/ossuarios/blocos?codigo_cliente=${produto.codigo_cliente}&codigo_estacao=${produto.codigo_estacao}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar blocos');
      }

      const data = await response.json();
      setBlocos(data);
    } catch (error) {
      console.error('Erro ao carregar blocos:', error);
      setError('Erro ao carregar blocos disponíveis');
    }
  };

  const loadNichosDisponiveis = async (codigoBloco) => {
    try {
      const response = await fetch(`/api/ossuarios/nichos?codigo_cliente=${produto.codigo_cliente}&codigo_estacao=${produto.codigo_estacao}&codigo_bloco=${codigoBloco}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar nichos');
      }

      const data = await response.json();
      // Filtrar apenas nichos que têm urnas disponíveis
      const nichosComUrnas = data.filter(nicho => nicho.urnas_disponiveis > 0);
      setNichosDisponiveis(nichosComUrnas);
    } catch (error) {
      console.error('Erro ao carregar nichos:', error);
      setError('Erro ao carregar nichos disponíveis');
    }
  };

  const loadUrnasDisponiveis = async (codigoBloco, numeroNicho) => {
    try {
      const response = await fetch(`/api/ossuarios/urnas?codigo_cliente=${produto.codigo_cliente}&codigo_estacao=${produto.codigo_estacao}&codigo_bloco=${codigoBloco}&numero_nicho=${numeroNicho}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar urnas');
      }

      const data = await response.json();
      // Filtrar apenas urnas disponíveis
      const urnasLivres = data.filter(urna => urna.disponivel);
      setUrnasDisponiveis(urnasLivres);
    } catch (error) {
      console.error('Erro ao carregar urnas:', error);
      setError('Erro ao carregar urnas disponíveis');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBlocoChange = (e) => {
    const codigoBloco = e.target.value;
    setFormData(prev => ({
      ...prev,
      codigo_bloco: codigoBloco,
      numero_nicho: '',
      letra_urna: ''
    }));
    
    if (codigoBloco) {
      loadNichosDisponiveis(codigoBloco);
    } else {
      setNichosDisponiveis([]);
      setUrnasDisponiveis([]);
    }
  };

  const handleNichoChange = (e) => {
    const numeroNicho = e.target.value;
    setFormData(prev => ({
      ...prev,
      numero_nicho: numeroNicho,
      letra_urna: ''
    }));
    
    if (numeroNicho && formData.codigo_bloco) {
      loadUrnasDisponiveis(formData.codigo_bloco, numeroNicho);
    } else {
      setUrnasDisponiveis([]);
    }
  };

  const getTipoCapacidadeLabel = (tipo) => {
    const labels = {
      1: 'Simples',
      2: 'Duplo',
      3: 'Triplo',
      4: 'Quádruplo'
    };
    return labels[tipo] || 'Desconhecido';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validações
    if (!formData.nome_sepultado || !formData.data_sepultamento || !formData.codigo_bloco || !formData.numero_nicho || !formData.letra_urna) {
      setError('Todos os campos obrigatórios devem ser preenchidos');
      setLoading(false);
      return;
    }

    try {
      const dadosParaEnvio = {
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        ...formData
      };

      let response;
      if (sepultamento) {
        // Atualizar sepultamento existente
        response = await fetch(`/api/ossuarios/sepultamentos/${sepultamento.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            nome_sepultado: formData.nome_sepultado,
            data_sepultamento: formData.data_sepultamento,
            hora_sepultamento: formData.hora_sepultamento,
            observacoes: formData.observacoes
          })
        });
      } else {
        // Criar novo sepultamento
        response = await fetch('/api/ossuarios/sepultamentos', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(dadosParaEnvio)
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao salvar sepultamento');
      }

      const result = await response.json();
      console.log('✅ Sepultamento salvo com sucesso:', result);
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao salvar sepultamento:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRemover = async () => {
    if (!sepultamento) return;
    
    if (!confirm('Tem certeza que deseja remover este sepultamento? O histórico será mantido e a urna ficará disponível.')) {
      return;
    }

    try {
      const response = await fetch(`/api/ossuarios/sepultamentos/${sepultamento.id}/remover`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          data_remocao: new Date().toISOString()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao remover sepultamento');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao remover sepultamento:', error);
      setError(error.message);
    }
  };

  const handleDeletar = async () => {
    if (!sepultamento) return;
    
    if (!confirm('ATENÇÃO: Tem certeza que deseja DELETAR PERMANENTEMENTE este sepultamento? Esta ação não pode ser desfeita e todo o histórico será perdido.')) {
      return;
    }

    try {
      const response = await fetch(`/api/ossuarios/sepultamentos/${sepultamento.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao deletar sepultamento');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao deletar sepultamento:', error);
      setError(error.message);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={sepultamento ? 'Editar Sepultamento em Ossuário' : 'Novo Sepultamento em Ossuário'}
      maxWidth="700px"
    >
      <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        
        {/* Informações do Produto */}
        <Paper sx={{ p: 2, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 1 }}>
            🏢 Produto: {produto?.denominacao}
          </Typography>
          <Typography variant="body2" sx={{ color: '#2e7d32' }}>
            <strong>Cliente:</strong> {produto?.codigo_cliente} | <strong>Estação:</strong> {produto?.codigo_estacao}
            <br />
            <Chip label="OSSUÁRIO" size="small" color="secondary" sx={{ mt: 1 }} />
          </Typography>
        </Paper>

        {/* Dados do Sepultado */}
        <TextField
          fullWidth
          required
          label="Nome do Sepultado"
          name="nome_sepultado"
          value={formData.nome_sepultado}
          onChange={handleChange}
          placeholder="Nome completo do sepultado"
        />

        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            fullWidth
            required
            type="date"
            label="Data de Sepultamento"
            name="data_sepultamento"
            value={formData.data_sepultamento}
            onChange={handleChange}
            InputLabelProps={{ shrink: true }}
          />
          <TextField
            fullWidth
            type="time"
            label="Hora de Sepultamento"
            name="hora_sepultamento"
            value={formData.hora_sepultamento}
            onChange={handleChange}
            InputLabelProps={{ shrink: true }}
          />
        </Box>

        <Divider />

        {/* Seleção de Localização */}
        <Typography variant="h6" color="primary">
          📍 Localização no Ossuário
        </Typography>

        <FormControl fullWidth required>
          <InputLabel>Bloco</InputLabel>
          <Select
            name="codigo_bloco"
            value={formData.codigo_bloco}
            label="Bloco"
            onChange={handleBlocoChange}
            disabled={!!sepultamento} // Não permite alterar localização em edição
          >
            <MenuItem value="">Selecione um bloco</MenuItem>
            {blocos.map(bloco => (
              <MenuItem key={bloco.codigo_bloco} value={bloco.codigo_bloco}>
                {bloco.nome} ({getTipoCapacidadeLabel(bloco.tipo_capacidade)} - {bloco.urnas_disponiveis} urnas disponíveis)
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth required>
          <InputLabel>Nicho</InputLabel>
          <Select
            name="numero_nicho"
            value={formData.numero_nicho}
            label="Nicho"
            onChange={handleNichoChange}
            disabled={!formData.codigo_bloco || !!sepultamento}
          >
            <MenuItem value="">Selecione um nicho</MenuItem>
            {nichosDisponiveis.map(nicho => (
              <MenuItem key={nicho.numero_nicho} value={nicho.numero_nicho}>
                Nicho {nicho.numero_nicho.toString().padStart(4, '0')} ({nicho.urnas_disponiveis}/{nicho.total_urnas} urnas disponíveis)
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth required>
          <InputLabel>Urna</InputLabel>
          <Select
            name="letra_urna"
            value={formData.letra_urna}
            label="Urna"
            onChange={handleChange}
            disabled={!formData.numero_nicho || !!sepultamento}
          >
            <MenuItem value="">Selecione uma urna</MenuItem>
            {urnasDisponiveis.map(urna => (
              <MenuItem key={urna.letra_urna} value={urna.letra_urna}>
                Urna {urna.letra_urna}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {sepultamento && (
          <Paper sx={{ p: 2, bgcolor: '#f5f5f5', borderLeft: '4px solid #9e9e9e' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#424242', mb: 1 }}>
              📍 Localização Atual
            </Typography>
            <Typography variant="body2" sx={{ color: '#424242' }}>
              <strong>Bloco:</strong> {sepultamento.codigo_bloco} | <strong>Nicho:</strong> {sepultamento.numero_nicho} | <strong>Urna:</strong> {sepultamento.letra_urna}
              <br />
              <em>A localização não pode ser alterada. Para mover, remova e crie um novo sepultamento.</em>
            </Typography>
          </Paper>
        )}

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Observações"
          name="observacoes"
          value={formData.observacoes}
          onChange={handleChange}
          placeholder="Observações sobre o sepultamento..."
        />

        {error && (
          <Alert severity="error">
            {error}
          </Alert>
        )}

        {/* Botões de Ação */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
          <StandardButton
            variant="outlined"
            onClick={onClose}
          >
            Cancelar
          </StandardButton>
          
          {sepultamento && (
            <>
              <StandardButton
                variant="contained"
                color="warning"
                onClick={handleRemover}
              >
                Remover (Manter Histórico)
              </StandardButton>
              <StandardButton
                variant="contained"
                color="error"
                onClick={handleDeletar}
              >
                Deletar (Apagar Histórico)
              </StandardButton>
            </>
          )}
          
          <StandardButton
            type="submit"
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Salvando...' : (sepultamento ? 'Atualizar' : 'Criar Sepultamento')}
          </StandardButton>
        </Box>
      </Box>
    </Modal>
  );
};

export default SepultamentoOssuarioModal;
