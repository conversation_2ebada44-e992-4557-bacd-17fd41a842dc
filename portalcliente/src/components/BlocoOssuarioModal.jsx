import React, { useState, useEffect } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Alert,
  Typography,
  Paper,
  Chip,
} from '@mui/material';
import Modal from './Modal';
import { StandardButton } from './common';

const BlocoOssuarioModal = ({ isOpen, onClose, bloco = null, produto, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_bloco: '',
    nome: '',
    descricao: '',
    tipo_capacidade: 1
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasNichos, setHasNichos] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (bloco) {
        setFormData({
          codigo_bloco: bloco.codigo_bloco || '',
          nome: bloco.nome || '',
          descricao: bloco.descricao || '',
          tipo_capacidade: bloco.tipo_capacidade || 1
        });
        setHasNichos(bloco.total_nichos > 0);
      } else {
        setFormData({
          codigo_bloco: '',
          nome: '',
          descricao: '',
          tipo_capacidade: 1
        });
        setHasNichos(false);
      }
      setError('');
    }
  }, [isOpen, bloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    let formattedValue = value;
    if (name === 'codigo_bloco') {
      formattedValue = value.toUpperCase();
    }

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const getTipoCapacidadeLabel = (tipo) => {
    const labels = {
      1: 'Simples (1 urna por nicho)',
      2: 'Duplo (2 urnas por nicho)',
      3: 'Triplo (3 urnas por nicho)',
      4: 'Quádruplo (4 urnas por nicho)'
    };
    return labels[tipo] || 'Desconhecido';
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validações
    if (!formData.codigo_bloco) {
      setError('Código do bloco é obrigatório');
      setLoading(false);
      return;
    }

    if (!formData.nome) {
      setError('Nome do bloco é obrigatório');
      setLoading(false);
      return;
    }

    try {
      const dadosParaEnvio = {
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        ...formData
      };

      let response;
      if (bloco) {
        // Atualizar bloco existente
        response = await fetch(`/api/ossuarios/blocos/${produto.codigo_cliente}/${produto.codigo_estacao}/${bloco.codigo_bloco}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            nome: formData.nome,
            descricao: formData.descricao
          })
        });
      } else {
        // Criar novo bloco
        response = await fetch('/api/ossuarios/blocos', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(dadosParaEnvio)
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao salvar bloco');
      }

      const result = await response.json();
      console.log('✅ Bloco salvo com sucesso:', result);
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao salvar bloco:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={bloco ? 'Editar Bloco de Ossuário' : 'Novo Bloco de Ossuário'}
      maxWidth="600px"
    >
      <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        
        {/* Informações do Produto */}
        <Paper sx={{ p: 2, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>
          <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 1 }}>
            🏢 Produto: {produto?.denominacao}
          </Typography>
          <Typography variant="body2" sx={{ color: '#2e7d32' }}>
            <strong>Cliente:</strong> {produto?.codigo_cliente} | <strong>Estação:</strong> {produto?.codigo_estacao}
            <br />
            <Chip label="OSSUÁRIO" size="small" color="secondary" sx={{ mt: 1 }} />
          </Typography>
        </Paper>

        {/* Aviso sobre edição */}
        {bloco && hasNichos && (
          <Paper sx={{ p: 2, bgcolor: '#fff3cd', borderLeft: '4px solid #ffc107' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#856404', mb: 1 }}>
              ⚠️ Bloco com Nichos Criados
            </Typography>
            <Typography variant="body2" sx={{ color: '#856404' }}>
              • <strong>Tipo de Capacidade:</strong> Não pode ser alterado (já existem {bloco?.total_nichos} nichos criados)
              <br />
              • <strong>Campos Editáveis:</strong> Nome e Descrição apenas
              <br />
              • <strong>Para alterar capacidade:</strong> Delete todos os ranges/nichos primeiro
            </Typography>
          </Paper>
        )}

        {/* Informações sobre criação */}
        {!bloco && (
          <Paper sx={{ p: 2, bgcolor: '#e3f2fd', borderLeft: '4px solid #2196f3' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 1 }}>
              ℹ️ Criando Novo Bloco
            </Typography>
            <Typography variant="body2" sx={{ color: '#1976d2' }}>
              • <strong>Tipo de Capacidade:</strong> Define quantas urnas cada nicho terá
              <br />
              • <strong>Após criação:</strong> Você poderá criar ranges de nichos
              <br />
              • <strong>Importante:</strong> O tipo não pode ser alterado após criar nichos
            </Typography>
          </Paper>
        )}

        {/* Campos do formulário */}
        {!bloco && (
          <TextField
            fullWidth
            required
            label="Código do Bloco"
            name="codigo_bloco"
            value={formData.codigo_bloco}
            onChange={handleChange}
            placeholder="Ex: BL_001"
            helperText="Código único para identificar o bloco"
          />
        )}

        {bloco && (
          <Paper sx={{ p: 2, bgcolor: '#f5f5f5', borderLeft: '4px solid #9e9e9e' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#424242', mb: 1 }}>
              📝 Bloco em Edição
            </Typography>
            <Typography variant="body2" sx={{ color: '#424242' }}>
              <strong>Código:</strong> {bloco.codigo_bloco}
              <br />
              <strong>Tipo:</strong> {getTipoCapacidadeLabel(bloco.tipo_capacidade)}
              <br />
              <strong>Nichos:</strong> {bloco.total_nichos} | <strong>Urnas:</strong> {bloco.total_urnas}
            </Typography>
          </Paper>
        )}

        <TextField
          fullWidth
          required
          label="Nome do Bloco"
          name="nome"
          value={formData.nome}
          onChange={handleChange}
          placeholder="Ex: BLOCO 01"
        />

        <TextField
          fullWidth
          label="Descrição"
          name="descricao"
          value={formData.descricao}
          onChange={handleChange}
          multiline
          rows={3}
          placeholder="Descrição detalhada do bloco..."
        />

        {!bloco && (
          <FormControl fullWidth required>
            <InputLabel>Tipo de Capacidade</InputLabel>
            <Select
              name="tipo_capacidade"
              value={formData.tipo_capacidade}
              label="Tipo de Capacidade"
              onChange={handleChange}
            >
              <MenuItem value={1}>Simples (1 urna por nicho)</MenuItem>
              <MenuItem value={2}>Duplo (2 urnas por nicho)</MenuItem>
              <MenuItem value={3}>Triplo (3 urnas por nicho)</MenuItem>
              <MenuItem value={4}>Quádruplo (4 urnas por nicho)</MenuItem>
            </Select>
          </FormControl>
        )}

        {error && (
          <Alert severity="error">
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
          <StandardButton
            variant="outlined"
            onClick={onClose}
          >
            Cancelar
          </StandardButton>
          <StandardButton
            type="submit"
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Salvando...' : (bloco ? 'Atualizar' : 'Criar Bloco')}
          </StandardButton>
        </Box>
      </Box>
    </Modal>
  );
};

export default BlocoOssuarioModal;
