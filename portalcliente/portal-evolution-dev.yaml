# ===================================
# PORTAL EVOLUTION - DESENVOLVIMENTO
# ===================================
# Arquivo de orquestração para ambiente de desenvolvimento
# Conecta à rede traefik existente - portaldev.evo-eden.site

version: '3.8'

services:
  # ===================================
  # BACKEND NODE.JS - DESENVOLVIMENTO
  # ===================================
  portal-backend-dev:
    image: portal-evolution-backend:v2025-08-28-ossuarios-fase1-dev
    container_name: portal-backend-dev
    restart: always
    environment:
      # Variáveis de ambiente do backend
      NODE_ENV: ${NODE_ENV:-development}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      PORT: ${PORT:-3002}
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS:-jgvhevmyjpuucbhp}
      TZ: ${TZ:-America/Sao_Paulo}
    networks:
      - traefik
      - portal_dev_internal
    healthcheck:
      # Verificação de saúde do backend
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3002/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: ${BACKEND_MEMORY:-512m}
        reservations:
          memory: 256M
      labels:
        # Configuração Traefik para roteamento da API - DESENVOLVIMENTO
        - "traefik.enable=true"
        - "traefik.http.routers.portal-dev-api.rule=Host(`portaldev.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portal-dev-api.entrypoints=websecure"
        - "traefik.http.routers.portal-dev-api.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-dev-api.loadbalancer.server.port=3002"
        - "portal.service=backend-dev"

  # ===================================
  # FRONTEND REACT + NGINX - DESENVOLVIMENTO
  # ===================================
  portal-frontend-dev:
    image: portal-evolution-frontend:v2025-08-28-ossuarios-fase1-dev
    container_name: portal-frontend-dev
    restart: always
    environment:
      TZ: ${TZ:-America/Sao_Paulo}
    depends_on:
      - portal-backend-dev
    networks:
      - traefik
    healthcheck:
      # Verificação de saúde do frontend
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: ${FRONTEND_MEMORY:-256m}
        reservations:
          memory: 128M
      labels:
        # Configuração Traefik para roteamento principal - DESENVOLVIMENTO
        - "traefik.enable=true"
        - "traefik.http.routers.portal-dev-web.rule=Host(`portaldev.evo-eden.site`)"
        - "traefik.http.routers.portal-dev-web.entrypoints=websecure"
        - "traefik.http.routers.portal-dev-web.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-dev-web.loadbalancer.server.port=80"
        - "portal.service=frontend-dev"

# ===================================
# REDES
# ===================================
networks:
  # Rede externa Traefik (já deve existir na VPS)
  traefik:
    external: true
    name: redeinterna
  
  # Rede interna para comunicação entre serviços - DESENVOLVIMENTO
  portal_dev_internal:
    driver: overlay
    internal: false

# ===================================
# INSTRUÇÕES DE USO - DESENVOLVIMENTO
# ===================================

# Para iniciar todos os serviços de desenvolvimento:
# docker-compose -f portal-evolution-dev.yaml up -d

# Para verificar status:
# docker-compose -f portal-evolution-dev.yaml ps

# Para ver logs:
# docker-compose -f portal-evolution-dev.yaml logs -f

# Para parar todos os serviços:
# docker-compose -f portal-evolution-dev.yaml down

# Para atualizar (rebuild):
# docker-compose -f portal-evolution-dev.yaml up -d --build
